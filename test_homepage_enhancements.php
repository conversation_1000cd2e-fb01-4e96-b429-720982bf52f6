<?php
/**
 * Test Homepage Enhancements - Arabic E-commerce Website
 * Validates the enhanced Influencers section, Success Story section, and Brand Timeline section
 */

// Test configuration
$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $condition, $description = '') {
    global $testResults, $totalTests, $passedTests;
    $totalTests++;
    $result = $condition ? 'PASS' : 'FAIL';
    if ($condition) $passedTests++;
    
    $testResults[] = [
        'name' => $testName,
        'result' => $result,
        'description' => $description
    ];
    
    return $condition;
}

// Check if files exist
$indexFile = 'index.php';
$cssFile = 'assets/css/homepage.css';

$indexExists = file_exists($indexFile);
$cssExists = file_exists($cssFile);

runTest('Index File Exists', $indexExists, 'Homepage file is present');
runTest('CSS File Exists', $cssExists, 'Homepage CSS file is present');

if ($indexExists) {
    $indexContent = file_get_contents($indexFile);
    
    // Test Influencers Section Enhancements
    runTest('Enhanced Influencers Section', 
        strpos($indexContent, 'influencers-section') !== false,
        'Enhanced influencers section class found');
    
    runTest('Influencer Card Enhanced', 
        strpos($indexContent, 'influencer-card-enhanced') !== false,
        'Enhanced influencer card class found');
    
    runTest('Influencer Header Enhanced', 
        strpos($indexContent, 'influencer-header') !== false,
        'Enhanced influencer header class found');
    
    runTest('Content Type Badge', 
        strpos($indexContent, 'content-type-badge') !== false,
        'Content type badge for better contrast found');
    
    runTest('Rating Section Enhanced', 
        strpos($indexContent, 'rating-section') !== false,
        'Enhanced rating section found');
    
    // Test Success Story Section Enhancements
    runTest('Enhanced Success Story Section', 
        strpos($indexContent, 'success-story-section') !== false,
        'Enhanced success story section class found');
    
    runTest('Story Content Wrapper', 
        strpos($indexContent, 'story-content-wrapper') !== false,
        'Story content wrapper found');
    
    runTest('Additional Story Text', 
        strpos($indexContent, 'story-additional-text') !== false,
        'Additional descriptive paragraph found');
    
    runTest('Story Features', 
        strpos($indexContent, 'story-features') !== false,
        'Story features section found');
    
    runTest('Achievement Cards Enhanced', 
        strpos($indexContent, 'achievement-card') !== false,
        'Enhanced achievement cards found');
    
    // Test Brand Timeline Section
    runTest('Brand Timeline Section', 
        strpos($indexContent, 'brand-timeline-section') !== false,
        'Brand timeline section found');
    
    runTest('Timeline HTML Comment', 
        strpos($indexContent, '<!-- Brand Story Timeline Section -->') !== false,
        'Required HTML comment found');
    
    runTest('Timeline Wrapper', 
        strpos($indexContent, 'timeline-wrapper') !== false,
        'Timeline wrapper found');
    
    runTest('Timeline Items', 
        strpos($indexContent, 'timeline-item') !== false,
        'Timeline items found');
    
    runTest('Timeline Markers', 
        strpos($indexContent, 'timeline-marker') !== false,
        'Timeline markers found');
    
    runTest('Timeline Years', 
        substr_count($indexContent, 'timeline-year') >= 5,
        'Multiple timeline years found (2019-2023)');
    
    runTest('Future Vision Section', 
        strpos($indexContent, 'future-vision') !== false,
        'Future vision section found');
}

if ($cssExists) {
    $cssContent = file_get_contents($cssFile);
    
    // Test CSS Enhancements
    runTest('Influencer CSS Enhanced', 
        strpos($cssContent, '.influencer-card-enhanced') !== false,
        'Enhanced influencer card CSS found');
    
    runTest('Better Color Contrast CSS', 
        strpos($cssContent, '.influencer-name') !== false && 
        strpos($cssContent, 'color: #ffffff !important') !== false,
        'Better color contrast CSS implemented');
    
    runTest('Success Story CSS Enhanced', 
        strpos($cssContent, '.success-story-section') !== false,
        'Enhanced success story CSS found');
    
    runTest('Timeline CSS Complete', 
        strpos($cssContent, '.brand-timeline-section') !== false,
        'Brand timeline CSS found');
    
    runTest('Timeline Responsive Design', 
        strpos($cssContent, '@media (max-width: 992px)') !== false &&
        strpos($cssContent, '.timeline-line') !== false,
        'Timeline responsive design found');
    
    runTest('Arabic RTL Support', 
        strpos($cssContent, '[dir="rtl"]') !== false &&
        strpos($cssContent, '.timeline-right') !== false,
        'Arabic RTL support for timeline found');
    
    runTest('Professional Gradients', 
        substr_count($cssContent, 'linear-gradient') >= 10,
        'Professional gradient styling implemented');
    
    runTest('Hover Effects', 
        substr_count($cssContent, ':hover') >= 15,
        'Professional hover effects implemented');
    
    runTest('Box Shadows', 
        substr_count($cssContent, 'box-shadow') >= 10,
        'Professional shadow effects implemented');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            direction: rtl; 
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-pass { 
            color: #28a745; 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            margin: 0.25rem 0;
        }
        .test-fail { 
            color: #dc3545; 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            margin: 0.25rem 0;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
        }
        .progress-bar-custom {
            height: 10px;
            border-radius: 5px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="bi bi-check-circle"></i> اختبار تحسينات الصفحة الرئيسية</h1>
            <p>فحص شامل للتحسينات المطبقة على قسم المؤثرين وقصة النجاح والجدول الزمني للعلامة التجارية</p>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="test-section">
                    <h3><i class="bi bi-people-fill"></i> اختبار قسم المؤثرين المحسن</h3>
                    <?php
                    $influencerTests = array_slice($testResults, 2, 5);
                    foreach ($influencerTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> {$test['name']}: {$test['description']}</div>";
                    }
                    ?>
                </div>

                <div class="test-section">
                    <h3><i class="bi bi-trophy-fill"></i> اختبار قسم قصة النجاح المحسن</h3>
                    <?php
                    $storyTests = array_slice($testResults, 7, 5);
                    foreach ($storyTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> {$test['name']}: {$test['description']}</div>";
                    }
                    ?>
                </div>

                <div class="test-section">
                    <h3><i class="bi bi-clock-history"></i> اختبار الجدول الزمني للعلامة التجارية</h3>
                    <?php
                    $timelineTests = array_slice($testResults, 12, 6);
                    foreach ($timelineTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> {$test['name']}: {$test['description']}</div>";
                    }
                    ?>
                </div>

                <div class="test-section">
                    <h3><i class="bi bi-palette-fill"></i> اختبار التحسينات البصرية والتقنية</h3>
                    <?php
                    $cssTests = array_slice($testResults, 18);
                    foreach ($cssTests as $test) {
                        $class = $test['result'] === 'PASS' ? 'test-pass' : 'test-fail';
                        $icon = $test['result'] === 'PASS' ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
                        echo "<div class='{$class}'><i class='bi {$icon}'></i> {$test['name']}: {$test['description']}</div>";
                    }
                    ?>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="summary-card">
                    <h3><i class="bi bi-graph-up"></i> ملخص النتائج</h3>
                    <div class="mt-3">
                        <h2><?php echo $passedTests; ?>/<?php echo $totalTests; ?></h2>
                        <p>اختبار مكتمل بنجاح</p>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-custom" style="width: <?php echo ($passedTests/$totalTests)*100; ?>%"></div>
                        </div>
                        <p class="small">نسبة النجاح: <?php echo round(($passedTests/$totalTests)*100, 1); ?>%</p>
                    </div>
                </div>

                <div class="test-section mt-3">
                    <h5><i class="bi bi-info-circle"></i> الميزات المطبقة</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check text-success"></i> تحسين تباين الألوان في بطاقات المؤثرين</li>
                        <li><i class="bi bi-check text-success"></i> إعادة تصميم قسم قصة النجاح</li>
                        <li><i class="bi bi-check text-success"></i> إضافة الجدول الزمني للعلامة التجارية</li>
                        <li><i class="bi bi-check text-success"></i> دعم كامل للغة العربية RTL</li>
                        <li><i class="bi bi-check text-success"></i> تصميم متجاوب احترافي</li>
                        <li><i class="bi bi-check text-success"></i> تأثيرات بصرية متقدمة</li>
                    </ul>
                </div>

                <div class="test-section">
                    <h5><i class="bi bi-eye"></i> معاينة الصفحة</h5>
                    <a href="index.php" class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-house-fill"></i> عرض الصفحة الرئيسية
                    </a>
                    <a href="test_homepage_redesign.php" class="btn btn-secondary w-100">
                        <i class="bi bi-gear-fill"></i> اختبار شامل آخر
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
